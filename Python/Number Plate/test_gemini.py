#!/usr/bin/env python3
"""
Simple test script to debug Gemini API issues
"""
import os
import google.generativeai as genai

def test_gemini():
    """Test basic Gemini API functionality"""
    try:
        # Get API key
        api_key = os.getenv('GEMINI_API_KEY')
        if not api_key:
            print("❌ No GEMINI_API_KEY environment variable set")
            return False
        
        print(f"✅ API key found: {api_key[:10]}...")
        
        # Configure client
        genai.configure(api_key=api_key)
        print("✅ Client configured")
        
        # Create model
        model = genai.GenerativeModel('gemini-1.5-flash')
        print("✅ Model created")
        
        # Test simple generation
        print("🔄 Testing simple text generation...")
        response = model.generate_content("Say hello")
        
        print(f"✅ Response received: {type(response)}")
        print(f"Response attributes: {[attr for attr in dir(response) if not attr.startswith('_')]}")
        
        # Try to get text
        try:
            text = response.text
            print(f"✅ Response text: {text}")
            return True
        except Exception as e:
            print(f"❌ Error getting text: {e}")
            
            # Try alternative methods
            try:
                if hasattr(response, 'parts'):
                    print(f"Response has parts: {len(response.parts)}")
                    for i, part in enumerate(response.parts):
                        print(f"Part {i}: {type(part)}")
                        if hasattr(part, 'text'):
                            print(f"Part {i} text: {part.text}")
            except Exception as e2:
                print(f"❌ Error accessing parts: {e2}")
            
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Testing Gemini API...")
    success = test_gemini()
    if success:
        print("🎉 Test successful!")
    else:
        print("💥 Test failed!")

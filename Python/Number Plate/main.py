"""
License Plate Monitoring System - Main Application
Continuously monitors RTSP camera and detects license plates using AI
"""
import time
import json
import os
import signal
import sys
import argparse
from datetime import datetime

# Import with fallback handling
try:
    import schedule
except ImportError:
    print("Error: 'schedule' library not installed. Run: pip install -r requirements.txt")
    sys.exit(1)

try:
    from dotenv import load_dotenv
except ImportError:
    print("Warning: 'python-dotenv' not installed. Environment variables won't be loaded from .env file")
    def load_dotenv():
        pass

# Camera handler imports with fallback to mock
try:
    from camera_handler import RTSPCameraHandler
    REAL_CAMERA_AVAILABLE = True
except ImportError:
    REAL_CAMERA_AVAILABLE = False
    print("Warning: Real camera handler not available. Using mock camera.")

from mock_camera import MockCameraHandler
from ai_processor import LicensePlateAIProcessor
from logger import get_logger, get_license_plate_logger


class LicensePlateMonitor:
    def __init__(self, config_path="config.json", use_mock_camera=False):
        """Initialize the license plate monitoring system"""
        # Load environment variables
        load_dotenv()

        self.config = self._load_config(config_path)
        self.logger = get_logger()
        self.license_plate_logger = get_license_plate_logger()

        # Initialize camera handler (real or mock)
        self.use_mock_camera = use_mock_camera
        if use_mock_camera:
            self.camera_handler = MockCameraHandler(config_path)
            self.logger.info("🎭 Using mock camera for testing")
        elif not REAL_CAMERA_AVAILABLE:
            self.camera_handler = MockCameraHandler(config_path)
            self.logger.warning("📷 Real camera not available (OpenCV issue), using mock camera")
        else:
            try:
                self.camera_handler = RTSPCameraHandler(config_path)
                self.logger.info("📹 Initializing real RTSP camera")
            except Exception as e:
                self.logger.warning(f"Failed to initialize real camera: {e}")
                self.logger.info("🎭 Falling back to mock camera")
                self.camera_handler = MockCameraHandler(config_path)
                self.use_mock_camera = True

        # Initialize AI processor
        self.ai_processor = LicensePlateAIProcessor(config_path)

        self.running = False

        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

    def _load_config(self, config_path):
        """Load configuration from JSON file"""
        try:
            with open(config_path, 'r') as f:
                config = json.load(f)

            # Override with environment variables if present
            if os.getenv('RTSP_URL'):
                config['rtsp']['url'] = os.getenv('RTSP_URL')
            if os.getenv('RTSP_USERNAME'):
                config['rtsp']['username'] = os.getenv('RTSP_USERNAME')
            if os.getenv('RTSP_PASSWORD'):
                config['rtsp']['password'] = os.getenv('RTSP_PASSWORD')
            if os.getenv('CAPTURE_INTERVAL_MINUTES'):
                config['capture']['interval_minutes'] = int(os.getenv('CAPTURE_INTERVAL_MINUTES'))

            return config
        except FileNotFoundError:
            self.logger.error(f"Config file {config_path} not found")
            sys.exit(1)

    def _signal_handler(self, signum, _frame):
        """Handle shutdown signals gracefully"""
        self.logger.info(f"Received signal {signum}, shutting down gracefully...")
        self.stop()

    def capture_and_process(self):
        """Capture image from camera and process it with AI"""
        print(f"\n📸 {datetime.now().strftime('%H:%M:%S')} - Capturing and processing...")

        try:
            # Connect to camera if not already connected
            if not self.camera_handler.is_connected():
                self.logger.warning("Camera disconnected, attempting to reconnect...")
                if not self.camera_handler.connect():
                    self.logger.error("❌ Failed to reconnect to camera")
                    return

            # Capture image
            success, image_path = self.camera_handler.capture_frame()

            if success and image_path:
                self.logger.info(f"📷 Image captured: {os.path.basename(image_path)}")

                # Process image with AI for license plate detection
                self.logger.info("🤖 Processing with AI...")
                ai_result = self.ai_processor.process_image(image_path)

                if ai_result['success']:
                    plates_detected = ai_result.get('plates_detected', 0)

                    if plates_detected > 0:
                        self.logger.info(f"🎯 Found {plates_detected} license plate(s)!")

                        for i, plate in enumerate(ai_result['license_plates'], 1):
                            plate_number = plate.get('plate_number', 'Unknown')
                            location = plate.get('location', 'Unknown location')
                            confidence = plate.get('confidence', 'Unknown')

                            # Clean and format the detection info
                            clean_location = location.replace('On a blue license plate, part of a vehicle', 'Vehicle').replace('On the rear of a blue rectangular object', 'Rear of vehicle')

                            self.logger.info(f"🏷️  Plate {i}: {plate_number} | {clean_location} | Confidence: {confidence}")

                            # Log to license plate specific logger
                            self.license_plate_logger.info(
                                f"DETECTED: {plate_number} | {clean_location} | {confidence} | {os.path.basename(image_path)}"
                            )

                            # Print to console for immediate visibility
                            print(f"   🏷️  {plate_number} ({confidence} confidence)")
                    else:
                        self.logger.info("ℹ️  No license plates detected")
                        print("   ℹ️  No plates detected")
                else:
                    error_msg = ai_result.get('error', 'Unknown AI processing error')
                    self.logger.error(f"❌ AI processing failed: {error_msg}")

            else:
                self.logger.error("❌ Failed to capture image")

        except Exception as e:
            self.logger.error(f"❌ Error during capture/processing: {str(e)}")

    def start(self):
        """Start the monitoring system"""
        print("\n🚀 Starting License Plate Monitoring System...")
        print("=" * 50)

        # Test AI processor first
        self.logger.info("🤖 Testing AI processor connection...")
        if not self.ai_processor.test_api_connection():
            self.logger.warning("AI processor test failed. License plate detection may not work.")
            print("⚠️  AI processing may be limited without valid API key")

        # Test camera connection with proper fallback
        self.logger.info("📷 Testing camera connection...")

        # If we're using real camera, try to connect with retries
        if not self.use_mock_camera and isinstance(self.camera_handler, RTSPCameraHandler):
            self.logger.info("🔄 Attempting to connect to real RTSP camera...")
            if self.camera_handler.connect():
                self.logger.info("✅ Real RTSP camera connected successfully!")
                print("📹 Connected to real RTSP camera")
            else:
                self.logger.warning("❌ Real camera connection failed after all attempts")
                self.logger.info("🎭 Switching to mock camera for demonstration")
                print("🎭 Falling back to mock camera")
                self.camera_handler = MockCameraHandler()
                self.use_mock_camera = True

                if not self.camera_handler.connect():
                    self.logger.error("❌ Even mock camera failed. System cannot start.")
                    return False
        else:
            # Using mock camera
            if not self.camera_handler.connect():
                self.logger.error("❌ Camera connection failed. System cannot start.")
                return False

        # Setup scheduling
        interval_minutes = self.config.get('capture', {}).get('interval_minutes', 1)
        self.logger.info(f"⏰ Scheduling capture every {interval_minutes} minute(s)")

        schedule.every(interval_minutes).minutes.do(self.capture_and_process)

        self.running = True
        print(f"✅ System started successfully!")
        print(f"⏰ Monitoring every {interval_minutes} minute(s)")
        print(f"📁 Images saved to: captured_images/")
        print(f"📝 Logs saved to: logs/")
        print("\n🔄 Monitoring active... (Press Ctrl+C to stop)")
        print("-" * 50)

        try:
            # Run the scheduler
            while self.running:
                schedule.run_pending()
                time.sleep(1)

        except KeyboardInterrupt:
            print("\n\n🛑 Stopping system...")
            self.logger.info("Received keyboard interrupt")
        finally:
            self.stop()

        return True

    def stop(self):
        """Stop the monitoring system"""
        self.logger.info("🛑 Stopping License Plate Monitoring System...")
        self.running = False

        # Disconnect camera
        self.camera_handler.disconnect()

        # Clear scheduled jobs
        schedule.clear()

        print("✅ System stopped successfully")
        self.logger.info("✅ License Plate Monitoring System stopped")

    def run_single_capture(self):
        """Run a single capture for testing purposes"""
        self.logger.info("Running single capture test...")

        with self.camera_handler:
            success, image_path = self.camera_handler.capture_frame()
            if success and image_path:
                self.logger.info(f"Single capture successful: {image_path}")
                return image_path
            else:
                self.logger.error("Single capture failed")
                return None


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description='License Plate Monitoring System')
    parser.add_argument('--test', action='store_true', help='Run a single capture test')
    parser.add_argument('--mock', action='store_true', help='Use mock camera instead of real RTSP camera')
    parser.add_argument('--ai-test', action='store_true', help='Test AI processor only')
    args = parser.parse_args()

    print("\n🚗 License Plate Monitoring System")
    print("=" * 50)

    # AI processor test
    if args.ai_test:
        print("Testing AI Processor...")
        processor = LicensePlateAIProcessor()
        if processor.test_api_connection():
            print("✓ AI Processor connection successful!")
            sys.exit(0)
        else:
            print("✗ AI Processor connection failed!")
            sys.exit(1)

    # Single capture test
    if args.test:
        print("Running single capture and AI processing test...")
        monitor = LicensePlateMonitor(use_mock_camera=args.mock)

        # Test camera
        if monitor.camera_handler.connect():
            success, image_path = monitor.camera_handler.capture_frame()
            if success and image_path:
                print(f"✓ Image captured: {image_path}")

                # Test AI processing
                print("Processing with AI...")
                ai_result = monitor.ai_processor.process_image(image_path)

                if ai_result['success']:
                    plates_detected = ai_result.get('plates_detected', 0)
                    print(f"✓ AI processing completed. License plates detected: {plates_detected}")

                    if plates_detected > 0:
                        for i, plate in enumerate(ai_result['license_plates'], 1):
                            plate_number = plate.get('plate_number', 'Unknown')
                            location = plate.get('location', 'Unknown location')
                            confidence = plate.get('confidence', 'Unknown')
                            print(f"  Plate {i}: {plate_number} (Location: {location}, Confidence: {confidence})")

                    print("Test successful!")
                    sys.exit(0)
                else:
                    error_msg = ai_result.get('error', 'Unknown error')
                    print(f"✗ AI processing failed: {error_msg}")
                    sys.exit(1)
            else:
                print("✗ Image capture failed!")
                sys.exit(1)
        else:
            print("✗ Camera connection failed!")
            sys.exit(1)

    # Normal operation
    print("Starting continuous monitoring...")
    monitor = LicensePlateMonitor(use_mock_camera=args.mock)

    try:
        success = monitor.start()
        if not success:
            sys.exit(1)
    except Exception as e:
        monitor.logger.error(f"Unexpected error: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
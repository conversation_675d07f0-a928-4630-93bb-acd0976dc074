"""
AI Processor for License Plate Detection using Google Gemini API
"""
import os
import json
import base64
from typing import Optional, List, Dict, Any
from PIL import Image
import io

try:
    import google.generativeai as genai
    GENAI_AVAILABLE = True
except ImportError:
    GENAI_AVAILABLE = False
    print("Warning: google-generativeai not available. AI processing will be disabled.")

from logger import get_logger, get_license_plate_logger


class LicensePlateAIProcessor:
    def __init__(self, config_path="config.json"):
        """Initialize the AI processor with Gemini API"""
        self.config = self._load_config(config_path)
        self.logger = get_logger()
        self.license_plate_logger = get_license_plate_logger()
        self.client = None
        self.model_name = self.config.get('gemini', {}).get('model', 'gemini-2.5-flash')
        
        if GENAI_AVAILABLE:
            self._initialize_client()
        else:
            self.logger.error("Google GenAI not available. Install with: pip install google-genai")
    
    def _load_config(self, config_path):
        """Load configuration from JSON file"""
        try:
            with open(config_path, 'r') as f:
                config = json.load(f)
            
            # Override with environment variables if present
            if os.getenv('GEMINI_API_KEY'):
                config.setdefault('gemini', {})['api_key'] = os.getenv('GEMINI_API_KEY')
            
            return config
        except FileNotFoundError:
            self.logger.error(f"Config file {config_path} not found")
            return {}
    
    def _initialize_client(self):
        """Initialize the Gemini API client"""
        try:
            api_key = self.config.get('gemini', {}).get('api_key', '') or os.getenv('GEMINI_API_KEY')

            if not api_key:
                self.logger.error("Gemini API key not configured. Set GEMINI_API_KEY environment variable or update config.json")
                return False

            # Configure the client with the API key
            genai.configure(api_key=api_key)
            self.client = genai

            self.logger.info("Gemini AI client initialized successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to initialize Gemini client: {str(e)}")
            return False
    
    def create_license_plate_prompt(self) -> str:
        """Create a detailed prompt for license plate detection"""
        return """
You are an expert computer vision system specialized in license plate detection and recognition. 

Analyze this image and:

1. **Detect License Plates**: Look for any license plates, number plates, or registration plates visible in the image
2. **Extract Text**: Read and transcribe the exact text/numbers on each license plate
3. **Provide Details**: For each license plate found, provide:
   - The complete license plate number/text
   - The location/position in the image (e.g., "front of white car", "rear of truck")
   - Confidence level (high/medium/low)
   - Any additional details (color, country/state if identifiable)

4. **Format Response**: Structure your response as:
   ```
   LICENSE PLATES DETECTED: [number]
   
   Plate 1:
   - Number: [exact text]
   - Location: [description]
   - Confidence: [high/medium/low]
   - Details: [additional info]
   
   Plate 2: (if any)
   - Number: [exact text]
   - Location: [description]
   - Confidence: [high/medium/low]
   - Details: [additional info]
   ```

If no license plates are found, respond with:
```
LICENSE PLATES DETECTED: 0
No license plates visible in this image.
```

Be thorough and accurate. Even partially visible or blurry plates should be noted with appropriate confidence levels.
"""
    
    def process_image(self, image_path: str) -> Dict[str, Any]:
        """Process an image to detect license plates"""
        if not GENAI_AVAILABLE or not self.client:
            return {
                'success': False,
                'error': 'Gemini AI not available',
                'license_plates': [],
                'raw_response': None
            }
        
        try:
            self.logger.info(f"Processing image for license plate detection: {image_path}")
            
            # Load and prepare the image
            if not os.path.exists(image_path):
                raise FileNotFoundError(f"Image file not found: {image_path}")
            
            # Open image with PIL
            image = Image.open(image_path)
            
            # Create the prompt
            prompt = self.create_license_plate_prompt()
            
            # Send to Gemini API
            model = self.client.GenerativeModel(self.model_name)
            response = model.generate_content([prompt, image])
            
            # Parse the response
            response_text = self._extract_response_text(response)
            result = self._parse_gemini_response(response_text, image_path)
            
            # Log the results
            self.license_plate_logger.log_api_event(True, response=result)
            self.license_plate_logger.log_license_plate_detected(
                result.get('license_plates', []), 
                image_path
            )
            
            return result
            
        except Exception as e:
            error_msg = f"Error processing image {image_path}: {str(e)}"
            self.logger.error(error_msg)
            self.license_plate_logger.log_api_event(False, error=error_msg)
            
            return {
                'success': False,
                'error': error_msg,
                'license_plates': [],
                'raw_response': None
            }
    
    def _extract_response_text(self, response) -> str:
        """Extract text from Gemini API response"""
        try:
            # Try accessing parts directly (this works)
            if hasattr(response, 'parts') and response.parts:
                text_parts = []
                for part in response.parts:
                    if hasattr(part, 'text'):
                        text_parts.append(part.text)
                return ''.join(text_parts)

            # Fallback to string representation
            return str(response)

        except Exception as e:
            self.logger.error(f"Error extracting response text: {e}")
            return str(response)

    def _parse_gemini_response(self, response_text: str, image_path: str) -> Dict[str, Any]:
        """Parse the Gemini API response to extract license plate information"""
        try:
            license_plates = []
            
            # Look for the number of plates detected
            lines = response_text.split('\n')
            plates_detected = 0
            
            for line in lines:
                if 'LICENSE PLATES DETECTED:' in line.upper():
                    try:
                        plates_detected = int(line.split(':')[1].strip())
                    except:
                        pass
                    break
            
            # If no plates detected, return early
            if plates_detected == 0:
                return {
                    'success': True,
                    'license_plates': [],
                    'plates_detected': 0,
                    'raw_response': response_text,
                    'image_path': image_path
                }
            
            # Parse individual plates
            current_plate = {}
            in_plate_section = False
            
            for line in lines:
                line = line.strip()
                
                if line.startswith('Plate ') and ':' in line:
                    # Save previous plate if exists
                    if current_plate:
                        license_plates.append(current_plate)
                    
                    # Start new plate
                    current_plate = {}
                    in_plate_section = True
                
                elif in_plate_section and line.startswith('- '):
                    # Parse plate details
                    if ':' in line:
                        key, value = line[2:].split(':', 1)
                        key = key.strip().lower()
                        value = value.strip()
                        
                        if key == 'number':
                            current_plate['plate_number'] = value
                        elif key == 'location':
                            current_plate['location'] = value
                        elif key == 'confidence':
                            current_plate['confidence'] = value
                        elif key == 'details':
                            current_plate['details'] = value
            
            # Add the last plate
            if current_plate:
                license_plates.append(current_plate)
            
            return {
                'success': True,
                'license_plates': license_plates,
                'plates_detected': len(license_plates),
                'raw_response': response_text,
                'image_path': image_path
            }
            
        except Exception as e:
            self.logger.error(f"Error parsing Gemini response: {str(e)}")
            return {
                'success': True,  # API call succeeded, but parsing failed
                'license_plates': [],
                'plates_detected': 0,
                'raw_response': response_text,
                'image_path': image_path,
                'parse_error': str(e)
            }
    
    def test_api_connection(self) -> bool:
        """Test the Gemini API connection"""
        if not GENAI_AVAILABLE or not self.client:
            self.logger.error("Gemini AI not available for testing")
            return False

        try:
            # Create a simple test
            model = self.client.GenerativeModel(self.model_name)
            response = model.generate_content("Hello, this is a test. Please respond with 'API connection successful'.")

            self.logger.debug(f"Response type: {type(response)}")
            self.logger.debug(f"Response attributes: {dir(response)}")

            if response:
                response_text = self._extract_response_text(response)

                if response_text:
                    self.logger.info(f"Gemini API connection test successful. Response: {response_text[:50]}...")
                    return True
                else:
                    self.logger.error("Gemini API test failed - no response text")
                    return False
            else:
                self.logger.error("Gemini API test failed - no response")
                return False

        except Exception as e:
            self.logger.error(f"Gemini API connection test failed: {str(e)}")
            return False


if __name__ == "__main__":
    # Test the AI processor
    processor = LicensePlateAIProcessor()
    
    if processor.test_api_connection():
        print("✓ AI Processor is ready!")
    else:
        print("✗ AI Processor setup failed")

2025-09-05 19:29:14,042 - LicensePlateMonitor - INFO - Logger test - INFO level
2025-09-05 19:29:14,042 - LicensePlateMonitor - WARNING - Logger test - WARNING level
2025-09-05 19:29:14,043 - LicensePlateMonitor - ERROR - Logger test - ERROR level
2025-09-05 19:29:14,043 - LicensePlateMonitor - INFO - Image captured successfully: test_image.jpg
2025-09-05 19:29:14,043 - LicensePlateMonitor - ERROR - Image capture failed: Test error
2025-09-05 19:29:14,043 - LicensePlateMonitor - INFO - Testing RTSP camera connection...
2025-09-05 19:29:14,043 - LicensePlateMonitor - INFO - Connecting to RTSP camera: 0
2025-09-05 19:29:14,044 - LicensePlateMonitor - ERROR - Failed to open RTSP stream
2025-09-05 19:29:14,044 - LicensePlateMonitor - ERROR - Camera test failed - could not connect
2025-09-05 19:29:14,044 - LicensePlateMonitor - INFO - Connecting to RTSP camera: 0
2025-09-05 19:29:14,044 - LicensePlateMonitor - ERROR - Failed to open RTSP stream
2025-09-05 19:29:14,045 - LicensePlateMonitor - INFO - Connecting to RTSP camera: 0
2025-09-05 19:29:14,045 - LicensePlateMonitor - ERROR - Failed to open RTSP stream
2025-09-05 19:29:14,045 - LicensePlateMonitor - INFO - Connecting to RTSP camera: 1
2025-09-05 19:29:14,045 - LicensePlateMonitor - ERROR - Failed to open RTSP stream
2025-09-05 19:33:10,698 - LicensePlateMonitor - INFO - Logger test - INFO level
2025-09-05 19:33:10,698 - LicensePlateMonitor - WARNING - Logger test - WARNING level
2025-09-05 19:33:10,698 - LicensePlateMonitor - ERROR - Logger test - ERROR level
2025-09-05 19:33:10,698 - LicensePlateMonitor - INFO - Image captured successfully: test_image.jpg
2025-09-05 19:33:10,698 - LicensePlateMonitor - ERROR - Image capture failed: Test error
2025-09-05 19:33:10,698 - LicensePlateMonitor - INFO - Testing RTSP camera connection...
2025-09-05 19:33:10,698 - LicensePlateMonitor - INFO - Connecting to RTSP camera: 0
2025-09-05 19:33:10,703 - LicensePlateMonitor - ERROR - Failed to open RTSP stream
2025-09-05 19:33:10,703 - LicensePlateMonitor - ERROR - Camera test failed - could not connect
2025-09-05 19:33:10,703 - LicensePlateMonitor - INFO - Connecting to RTSP camera: 0
2025-09-05 19:33:10,703 - LicensePlateMonitor - ERROR - Failed to open RTSP stream
2025-09-05 19:33:10,703 - LicensePlateMonitor - INFO - Connecting to RTSP camera: 0
2025-09-05 19:33:10,703 - LicensePlateMonitor - ERROR - Failed to open RTSP stream
2025-09-05 19:33:10,704 - LicensePlateMonitor - INFO - Connecting to RTSP camera: 1
2025-09-05 19:33:10,704 - LicensePlateMonitor - ERROR - Failed to open RTSP stream
2025-09-05 19:37:10,191 - LicensePlateMonitor - INFO - Running single capture test...
2025-09-05 19:37:10,191 - LicensePlateMonitor - INFO - Connecting to RTSP camera: rtsp://***:***@***:***@192.168.68.63:1935
2025-09-05 19:37:10,191 - LicensePlateMonitor - ERROR - Error connecting to RTSP camera: module 'cv2' has no attribute 'VideoCapture'
2025-09-05 19:37:10,191 - LicensePlateMonitor - ERROR - Camera not connected
2025-09-05 19:37:10,191 - LicensePlateMonitor - ERROR - Single capture failed
2025-09-05 19:37:10,191 - LicensePlateMonitor - INFO - Disconnected from RTSP camera
2025-09-05 19:42:36,143 - LicensePlateMonitor - INFO - Connecting to mock camera...
2025-09-05 19:42:37,149 - LicensePlateMonitor - INFO - Mock camera connected successfully
2025-09-05 19:42:37,164 - LicensePlateMonitor - INFO - Generated mock image with license plate: PQR901
2025-09-05 19:42:37,362 - LicensePlateMonitor - INFO - Mock frame captured and saved: captured_images/mock_capture_20250905_194237.jpg
2025-09-05 19:42:38,373 - LicensePlateMonitor - INFO - Generated mock image without license plate
2025-09-05 19:42:38,383 - LicensePlateMonitor - INFO - Mock frame captured and saved: captured_images/mock_capture_20250905_194238.jpg
2025-09-05 19:42:39,394 - LicensePlateMonitor - INFO - Generated mock image with license plate: STU234
2025-09-05 19:42:39,399 - LicensePlateMonitor - INFO - Mock frame captured and saved: captured_images/mock_capture_20250905_194239.jpg
2025-09-05 19:42:40,404 - LicensePlateMonitor - INFO - Disconnecting from mock camera
2025-09-05 19:43:15,112 - LicensePlateMonitor - ERROR - Google GenAI not available. Install with: pip install google-genai
2025-09-05 19:43:15,112 - LicensePlateMonitor - ERROR - Gemini AI not available for testing
2025-09-05 19:44:49,199 - LicensePlateMonitor - INFO - Gemini AI client initialized successfully
2025-09-05 19:44:49,718 - LicensePlateMonitor - ERROR - Gemini API connection test failed: 400 API key expired. Please renew the API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key expired. Please renew the API key."
]
2025-09-05 19:44:57,664 - LicensePlateMonitor - INFO - Using mock camera for testing
2025-09-05 19:44:57,664 - LicensePlateMonitor - INFO - Gemini AI client initialized successfully
2025-09-05 19:44:57,664 - LicensePlateMonitor - INFO - Connecting to mock camera...
2025-09-05 19:44:58,665 - LicensePlateMonitor - INFO - Mock camera connected successfully
2025-09-05 19:44:58,667 - LicensePlateMonitor - INFO - Generated mock image without license plate
2025-09-05 19:44:58,685 - LicensePlateMonitor - INFO - Mock frame captured and saved: captured_images/mock_capture_20250905_194458.jpg
2025-09-05 19:44:58,685 - LicensePlateMonitor - INFO - Processing image for license plate detection: captured_images/mock_capture_20250905_194458.jpg
2025-09-05 19:44:59,234 - LicensePlateMonitor - ERROR - Error processing image captured_images/mock_capture_20250905_194458.jpg: 400 API key expired. Please renew the API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key expired. Please renew the API key."
]
2025-09-05 19:44:59,234 - LicensePlateMonitor - ERROR - API call failed: Error processing image captured_images/mock_capture_20250905_194458.jpg: 400 API key expired. Please renew the API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key expired. Please renew the API key."
]
2025-09-05 19:46:11,283 - LicensePlateMonitor - INFO - Connecting to mock camera...
2025-09-05 19:46:12,289 - LicensePlateMonitor - INFO - Mock camera connected successfully
2025-09-05 19:46:12,304 - LicensePlateMonitor - INFO - Generated mock image with license plate: GHI012
2025-09-05 19:46:12,315 - LicensePlateMonitor - INFO - Mock frame captured and saved: captured_images/mock_capture_20250905_194612.jpg
2025-09-05 19:46:13,321 - LicensePlateMonitor - INFO - Generated mock image without license plate
2025-09-05 19:46:13,326 - LicensePlateMonitor - INFO - Mock frame captured and saved: captured_images/mock_capture_20250905_194613.jpg
2025-09-05 19:46:14,333 - LicensePlateMonitor - INFO - Generated mock image without license plate
2025-09-05 19:46:14,340 - LicensePlateMonitor - INFO - Mock frame captured and saved: captured_images/mock_capture_20250905_194614.jpg
2025-09-05 19:46:15,346 - LicensePlateMonitor - INFO - Disconnecting from mock camera
2025-09-05 19:46:15,346 - LicensePlateMonitor - INFO - Gemini AI client initialized successfully
2025-09-05 19:46:15,823 - LicensePlateMonitor - ERROR - Gemini API connection test failed: 400 API key expired. Please renew the API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key expired. Please renew the API key."
]
2025-09-05 19:48:31,783 - LicensePlateMonitor - INFO - Using mock camera for testing
2025-09-05 19:48:31,783 - LicensePlateMonitor - INFO - Gemini AI client initialized successfully
2025-09-05 19:48:31,783 - LicensePlateMonitor - INFO - Connecting to mock camera...
2025-09-05 19:48:32,788 - LicensePlateMonitor - INFO - Mock camera connected successfully
2025-09-05 19:48:32,809 - LicensePlateMonitor - INFO - Generated mock image with license plate: MNO678
2025-09-05 19:48:32,825 - LicensePlateMonitor - INFO - Mock frame captured and saved: captured_images/mock_capture_20250905_194832.jpg
2025-09-05 19:48:32,826 - LicensePlateMonitor - INFO - Processing image for license plate detection: captured_images/mock_capture_20250905_194832.jpg
2025-09-05 19:48:34,873 - LicensePlateMonitor - ERROR - Error processing image captured_images/mock_capture_20250905_194832.jpg: argument of type 'Part' is not iterable
2025-09-05 19:48:34,873 - LicensePlateMonitor - ERROR - API call failed: Error processing image captured_images/mock_capture_20250905_194832.jpg: argument of type 'Part' is not iterable
2025-09-05 19:49:02,662 - LicensePlateMonitor - INFO - Using real RTSP camera
2025-09-05 19:49:02,663 - LicensePlateMonitor - INFO - Gemini AI client initialized successfully
2025-09-05 19:49:02,663 - LicensePlateMonitor - INFO - Starting License Plate Monitoring System...
2025-09-05 19:49:02,663 - LicensePlateMonitor - INFO - Testing AI processor connection...
2025-09-05 19:49:03,339 - LicensePlateMonitor - ERROR - Gemini API connection test failed: argument of type 'Part' is not iterable
2025-09-05 19:49:03,340 - LicensePlateMonitor - WARNING - AI processor test failed. License plate detection may not work.
2025-09-05 19:49:03,340 - LicensePlateMonitor - INFO - Testing camera connection...
2025-09-05 19:49:03,340 - LicensePlateMonitor - INFO - Connecting to RTSP camera: rtsp://***:***@***:***@192.168.68.63:1935
2025-09-05 19:49:03,340 - LicensePlateMonitor - ERROR - Error connecting to RTSP camera: module 'cv2' has no attribute 'VideoCapture'
2025-09-05 19:49:03,341 - LicensePlateMonitor - ERROR - Camera connection failed. Please check your configuration.
2025-09-05 19:50:37,436 - LicensePlateMonitor - INFO - Gemini AI client initialized successfully
2025-09-05 19:50:39,714 - LicensePlateMonitor - ERROR - Gemini API connection test failed: argument of type 'Part' is not iterable
2025-09-05 19:51:04,957 - LicensePlateMonitor - INFO - Gemini AI client initialized successfully
2025-09-05 19:51:15,356 - LicensePlateMonitor - ERROR - Gemini API connection test failed: argument of type 'Part' is not iterable
2025-09-05 19:52:25,507 - LicensePlateMonitor - INFO - Gemini AI client initialized successfully
2025-09-05 19:52:26,531 - LicensePlateMonitor - INFO - Gemini API connection test successful. Response: API connection successful...
2025-09-05 19:52:33,520 - LicensePlateMonitor - INFO - Using mock camera for testing
2025-09-05 19:52:33,520 - LicensePlateMonitor - INFO - Gemini AI client initialized successfully
2025-09-05 19:52:33,520 - LicensePlateMonitor - INFO - Connecting to mock camera...
2025-09-05 19:52:34,521 - LicensePlateMonitor - INFO - Mock camera connected successfully
2025-09-05 19:52:34,534 - LicensePlateMonitor - INFO - Generated mock image with license plate: VWX567
2025-09-05 19:52:34,547 - LicensePlateMonitor - INFO - Mock frame captured and saved: captured_images/mock_capture_20250905_195234.jpg
2025-09-05 19:52:34,548 - LicensePlateMonitor - INFO - Processing image for license plate detection: captured_images/mock_capture_20250905_195234.jpg
2025-09-05 19:52:36,934 - LicensePlateMonitor - INFO - API call successful: {'success': True, 'license_plates': [{'plate_number': 'VWX567', 'location': 'On a blue sign positioned on the road.', 'confidence': 'High', 'details': 'The sign appears to be a mock or informational sign rather than a vehicle license plate.'}], 'plates_detected': 1, 'raw_response': 'LICENSE PLATES DETECTED: 1\n\nPlate 1:\n- Number: VWX567\n- Location: On a blue sign positioned on the road.\n- Confidence: High\n- Details: The sign appears to be a mock or informational sign rather than a vehicle license plate.', 'image_path': 'captured_images/mock_capture_20250905_195234.jpg'}
2025-09-05 19:52:36,934 - LicensePlateMonitor - INFO - License plates detected in captured_images/mock_capture_20250905_195234.jpg: [{'plate_number': 'VWX567', 'location': 'On a blue sign positioned on the road.', 'confidence': 'High', 'details': 'The sign appears to be a mock or informational sign rather than a vehicle license plate.'}]
2025-09-05 19:52:45,012 - LicensePlateMonitor - INFO - Using mock camera for testing
2025-09-05 19:52:45,012 - LicensePlateMonitor - INFO - Gemini AI client initialized successfully
2025-09-05 19:52:45,012 - LicensePlateMonitor - INFO - Starting License Plate Monitoring System...
2025-09-05 19:52:45,013 - LicensePlateMonitor - INFO - Testing AI processor connection...
2025-09-05 19:52:45,950 - LicensePlateMonitor - INFO - Gemini API connection test successful. Response: API connection successful...
2025-09-05 19:52:45,950 - LicensePlateMonitor - INFO - Testing camera connection...
2025-09-05 19:52:45,951 - LicensePlateMonitor - INFO - Connecting to mock camera...
2025-09-05 19:52:46,956 - LicensePlateMonitor - INFO - Mock camera connected successfully
2025-09-05 19:52:46,962 - LicensePlateMonitor - INFO - Scheduling image capture and AI processing every 1 minute(s)
2025-09-05 19:52:46,963 - LicensePlateMonitor - INFO - License Plate Monitoring System started successfully!
2025-09-05 19:52:46,963 - LicensePlateMonitor - INFO - System will capture images and detect license plates automatically...
2025-09-05 19:53:14,627 - LicensePlateMonitor - INFO - Received signal 15, shutting down gracefully...
2025-09-05 19:53:14,627 - LicensePlateMonitor - INFO - Stopping License Plate Monitoring System...
2025-09-05 19:53:14,627 - LicensePlateMonitor - INFO - Disconnecting from mock camera
2025-09-05 19:53:14,627 - LicensePlateMonitor - INFO - License Plate Monitoring System stopped.
2025-09-05 19:53:14,627 - LicensePlateMonitor - INFO - Received signal 15, shutting down gracefully...
2025-09-05 19:53:14,627 - LicensePlateMonitor - INFO - Stopping License Plate Monitoring System...
2025-09-05 19:53:14,627 - LicensePlateMonitor - INFO - License Plate Monitoring System stopped.
2025-09-05 19:53:15,082 - LicensePlateMonitor - INFO - Stopping License Plate Monitoring System...
2025-09-05 19:53:15,083 - LicensePlateMonitor - INFO - License Plate Monitoring System stopped.
2025-09-05 19:53:55,517 - LicensePlateMonitor - INFO - Using mock camera for testing
2025-09-05 19:53:55,517 - LicensePlateMonitor - INFO - Gemini AI client initialized successfully
2025-09-05 19:53:55,517 - LicensePlateMonitor - INFO - Starting License Plate Monitoring System...
2025-09-05 19:53:55,517 - LicensePlateMonitor - INFO - Testing AI processor connection...
2025-09-05 19:53:56,214 - LicensePlateMonitor - INFO - Gemini API connection test successful. Response: API connection successful...
2025-09-05 19:53:56,215 - LicensePlateMonitor - INFO - Testing camera connection...
2025-09-05 19:53:56,215 - LicensePlateMonitor - INFO - Connecting to mock camera...
2025-09-05 19:53:57,220 - LicensePlateMonitor - INFO - Mock camera connected successfully
2025-09-05 19:53:57,221 - LicensePlateMonitor - INFO - Scheduling image capture and AI processing every 1 minute(s)
2025-09-05 19:53:57,222 - LicensePlateMonitor - INFO - License Plate Monitoring System started successfully!
2025-09-05 19:53:57,222 - LicensePlateMonitor - INFO - System will capture images and detect license plates automatically...
2025-09-05 19:54:57,441 - LicensePlateMonitor - INFO - Starting scheduled image capture and AI processing...
2025-09-05 19:54:57,453 - LicensePlateMonitor - INFO - Generated mock image with license plate: ABC123
2025-09-05 19:54:57,464 - LicensePlateMonitor - INFO - Mock frame captured and saved: captured_images/mock_capture_20250905_195457.jpg
2025-09-05 19:54:57,464 - LicensePlateMonitor - INFO - Successfully captured image: captured_images/mock_capture_20250905_195457.jpg
2025-09-05 19:54:57,464 - LicensePlateMonitor - INFO - Processing image with Gemini AI...
2025-09-05 19:54:57,464 - LicensePlateMonitor - INFO - Processing image for license plate detection: captured_images/mock_capture_20250905_195457.jpg
2025-09-05 19:54:59,501 - LicensePlateMonitor - INFO - API call successful: {'success': True, 'license_plates': [{'plate_number': 'ABC123', 'location': 'On a blue license plate, part of a vehicle in the middle of the road.', 'confidence': 'high', 'details': 'The license plate is blue with white text.'}], 'plates_detected': 1, 'raw_response': 'LICENSE PLATES DETECTED: 1\n\nPlate 1:\n- Number: ABC123\n- Location: On a blue license plate, part of a vehicle in the middle of the road.\n- Confidence: high\n- Details: The license plate is blue with white text.', 'image_path': 'captured_images/mock_capture_20250905_195457.jpg'}
2025-09-05 19:54:59,501 - LicensePlateMonitor - INFO - License plates detected in captured_images/mock_capture_20250905_195457.jpg: [{'plate_number': 'ABC123', 'location': 'On a blue license plate, part of a vehicle in the middle of the road.', 'confidence': 'high', 'details': 'The license plate is blue with white text.'}]
2025-09-05 19:54:59,501 - LicensePlateMonitor - INFO - AI processing completed. License plates detected: 1
2025-09-05 19:54:59,501 - LicensePlateMonitor - INFO - Plate 1: ABC123 (Location: On a blue license plate, part of a vehicle in the middle of the road., Confidence: high)
2025-09-05 19:54:59,501 - LicensePlateMonitor - ERROR - Error during scheduled capture and processing: 'LicensePlateLogger' object has no attribute 'info'
2025-09-05 19:55:59,730 - LicensePlateMonitor - INFO - Starting scheduled image capture and AI processing...
2025-09-05 19:55:59,737 - LicensePlateMonitor - INFO - Generated mock image with license plate: STU234
2025-09-05 19:55:59,741 - LicensePlateMonitor - INFO - Mock frame captured and saved: captured_images/mock_capture_20250905_195559.jpg
2025-09-05 19:55:59,741 - LicensePlateMonitor - INFO - Successfully captured image: captured_images/mock_capture_20250905_195559.jpg
2025-09-05 19:55:59,741 - LicensePlateMonitor - INFO - Processing image with Gemini AI...
2025-09-05 19:55:59,741 - LicensePlateMonitor - INFO - Processing image for license plate detection: captured_images/mock_capture_20250905_195559.jpg
2025-09-05 19:56:01,784 - LicensePlateMonitor - INFO - API call successful: {'success': True, 'license_plates': [{'plate_number': 'STU234', 'location': 'On the rear of a blue rectangular object, centered horizontally in the image and positioned on the road.', 'confidence': 'high', 'details': 'The license plate appears to be white text on a dark blue background. It is clearly legible.'}], 'plates_detected': 1, 'raw_response': 'LICENSE PLATES DETECTED: 1\n\nPlate 1:\n- Number: STU234\n- Location: On the rear of a blue rectangular object, centered horizontally in the image and positioned on the road.\n- Confidence: high\n- Details: The license plate appears to be white text on a dark blue background. It is clearly legible.', 'image_path': 'captured_images/mock_capture_20250905_195559.jpg'}
2025-09-05 19:56:01,785 - LicensePlateMonitor - INFO - License plates detected in captured_images/mock_capture_20250905_195559.jpg: [{'plate_number': 'STU234', 'location': 'On the rear of a blue rectangular object, centered horizontally in the image and positioned on the road.', 'confidence': 'high', 'details': 'The license plate appears to be white text on a dark blue background. It is clearly legible.'}]
2025-09-05 19:56:01,786 - LicensePlateMonitor - INFO - AI processing completed. License plates detected: 1
2025-09-05 19:56:01,786 - LicensePlateMonitor - INFO - Plate 1: STU234 (Location: On the rear of a blue rectangular object, centered horizontally in the image and positioned on the road., Confidence: high)
2025-09-05 19:56:01,786 - LicensePlateMonitor - ERROR - Error during scheduled capture and processing: 'LicensePlateLogger' object has no attribute 'info'
2025-09-05 19:57:02,038 - LicensePlateMonitor - INFO - Starting scheduled image capture and AI processing...
2025-09-05 19:57:02,046 - LicensePlateMonitor - INFO - Generated mock image with license plate: STU234
2025-09-05 19:57:02,050 - LicensePlateMonitor - INFO - Mock frame captured and saved: captured_images/mock_capture_20250905_195702.jpg
2025-09-05 19:57:02,050 - LicensePlateMonitor - INFO - Successfully captured image: captured_images/mock_capture_20250905_195702.jpg
2025-09-05 19:57:02,050 - LicensePlateMonitor - INFO - Processing image with Gemini AI...
2025-09-05 19:57:02,050 - LicensePlateMonitor - INFO - Processing image for license plate detection: captured_images/mock_capture_20250905_195702.jpg
2025-09-05 19:57:04,425 - LicensePlateMonitor - INFO - API call successful: {'success': True, 'license_plates': [{'plate_number': 'STU234', 'location': 'On a blue rectangular sign centered on the road.', 'confidence': 'High', 'details': 'White text on a blue background. The sign appears to be a simulated license plate.'}], 'plates_detected': 1, 'raw_response': 'LICENSE PLATES DETECTED: 1\n\nPlate 1:\n- Number: STU234\n- Location: On a blue rectangular sign centered on the road.\n- Confidence: High\n- Details: White text on a blue background. The sign appears to be a simulated license plate.', 'image_path': 'captured_images/mock_capture_20250905_195702.jpg'}
2025-09-05 19:57:04,426 - LicensePlateMonitor - INFO - License plates detected in captured_images/mock_capture_20250905_195702.jpg: [{'plate_number': 'STU234', 'location': 'On a blue rectangular sign centered on the road.', 'confidence': 'High', 'details': 'White text on a blue background. The sign appears to be a simulated license plate.'}]
2025-09-05 19:57:04,426 - LicensePlateMonitor - INFO - AI processing completed. License plates detected: 1
2025-09-05 19:57:04,426 - LicensePlateMonitor - INFO - Plate 1: STU234 (Location: On a blue rectangular sign centered on the road., Confidence: High)
2025-09-05 19:57:04,426 - LicensePlateMonitor - ERROR - Error during scheduled capture and processing: 'LicensePlateLogger' object has no attribute 'info'
2025-09-05 19:58:04,708 - LicensePlateMonitor - INFO - Starting scheduled image capture and AI processing...
2025-09-05 19:58:04,717 - LicensePlateMonitor - INFO - Generated mock image with license plate: STU234
2025-09-05 19:58:04,724 - LicensePlateMonitor - INFO - Mock frame captured and saved: captured_images/mock_capture_20250905_195804.jpg
2025-09-05 19:58:04,725 - LicensePlateMonitor - INFO - Successfully captured image: captured_images/mock_capture_20250905_195804.jpg
2025-09-05 19:58:04,725 - LicensePlateMonitor - INFO - Processing image with Gemini AI...
2025-09-05 19:58:04,725 - LicensePlateMonitor - INFO - Processing image for license plate detection: captured_images/mock_capture_20250905_195804.jpg
2025-09-05 19:58:07,261 - LicensePlateMonitor - INFO - API call successful: {'success': True, 'license_plates': [{'plate_number': 'STU234', 'location': 'On a blue sign located on the road, to the right of the center.', 'confidence': 'High', 'details': 'The sign is blue and appears to be a road sign. The text "STU234" is clearly visible on the lower part of the sign.'}], 'plates_detected': 1, 'raw_response': 'LICENSE PLATES DETECTED: 1\n\nPlate 1:\n- Number: STU234\n- Location: On a blue sign located on the road, to the right of the center.\n- Confidence: High\n- Details: The sign is blue and appears to be a road sign. The text "STU234" is clearly visible on the lower part of the sign.', 'image_path': 'captured_images/mock_capture_20250905_195804.jpg'}
2025-09-05 19:58:07,261 - LicensePlateMonitor - INFO - License plates detected in captured_images/mock_capture_20250905_195804.jpg: [{'plate_number': 'STU234', 'location': 'On a blue sign located on the road, to the right of the center.', 'confidence': 'High', 'details': 'The sign is blue and appears to be a road sign. The text "STU234" is clearly visible on the lower part of the sign.'}]
2025-09-05 19:58:07,261 - LicensePlateMonitor - INFO - AI processing completed. License plates detected: 1
2025-09-05 19:58:07,261 - LicensePlateMonitor - INFO - Plate 1: STU234 (Location: On a blue sign located on the road, to the right of the center., Confidence: High)
2025-09-05 19:58:07,261 - LicensePlateMonitor - ERROR - Error during scheduled capture and processing: 'LicensePlateLogger' object has no attribute 'info'
2025-09-05 19:58:08,916 - LicensePlateMonitor - INFO - Received signal 2, shutting down gracefully...
2025-09-05 19:58:08,917 - LicensePlateMonitor - INFO - Stopping License Plate Monitoring System...
2025-09-05 19:58:08,917 - LicensePlateMonitor - INFO - Disconnecting from mock camera
2025-09-05 19:58:08,917 - LicensePlateMonitor - INFO - License Plate Monitoring System stopped.
2025-09-05 19:58:09,272 - LicensePlateMonitor - INFO - Stopping License Plate Monitoring System...
2025-09-05 19:58:09,273 - LicensePlateMonitor - INFO - License Plate Monitoring System stopped.
2025-09-05 19:58:15,585 - LicensePlateMonitor - INFO - Using real RTSP camera
2025-09-05 19:58:15,585 - LicensePlateMonitor - INFO - Gemini AI client initialized successfully
2025-09-05 19:58:15,585 - LicensePlateMonitor - INFO - Starting License Plate Monitoring System...
2025-09-05 19:58:15,585 - LicensePlateMonitor - INFO - Testing AI processor connection...
2025-09-05 19:58:16,347 - LicensePlateMonitor - INFO - Gemini API connection test successful. Response: API connection successful...
2025-09-05 19:58:16,347 - LicensePlateMonitor - INFO - Testing camera connection...
2025-09-05 19:58:16,348 - LicensePlateMonitor - INFO - Connecting to RTSP camera: rtsp://***:***@***:***@192.168.68.63:1935
2025-09-05 19:58:16,348 - LicensePlateMonitor - ERROR - Error connecting to RTSP camera: module 'cv2' has no attribute 'VideoCapture'
2025-09-05 19:58:16,348 - LicensePlateMonitor - WARNING - Camera connection failed. Checking if we can fall back to mock camera...
2025-09-05 19:58:16,348 - LicensePlateMonitor - INFO - Switching to mock camera for demonstration
2025-09-05 19:58:16,349 - LicensePlateMonitor - INFO - Connecting to mock camera...
2025-09-05 19:58:17,354 - LicensePlateMonitor - INFO - Mock camera connected successfully
2025-09-05 19:58:17,357 - LicensePlateMonitor - INFO - Scheduling image capture and AI processing every 1 minute(s)
2025-09-05 19:58:17,357 - LicensePlateMonitor - INFO - License Plate Monitoring System started successfully!
2025-09-05 19:58:17,358 - LicensePlateMonitor - INFO - System will capture images and detect license plates automatically...
2025-09-05 19:58:28,363 - LicensePlateMonitor - INFO - Received signal 2, shutting down gracefully...
2025-09-05 19:58:28,363 - LicensePlateMonitor - INFO - Stopping License Plate Monitoring System...
2025-09-05 19:58:28,363 - LicensePlateMonitor - INFO - Disconnecting from mock camera
2025-09-05 19:58:28,363 - LicensePlateMonitor - INFO - License Plate Monitoring System stopped.
2025-09-05 19:58:28,398 - LicensePlateMonitor - INFO - Stopping License Plate Monitoring System...
2025-09-05 19:58:28,398 - LicensePlateMonitor - INFO - License Plate Monitoring System stopped.
2025-09-05 19:58:52,657 - LicensePlateMonitor - INFO - Using real RTSP camera
2025-09-05 19:58:52,657 - LicensePlateMonitor - INFO - Gemini AI client initialized successfully
2025-09-05 19:58:52,657 - LicensePlateMonitor - INFO - Starting License Plate Monitoring System...
2025-09-05 19:58:52,657 - LicensePlateMonitor - INFO - Testing AI processor connection...
2025-09-05 19:58:53,414 - LicensePlateMonitor - INFO - Gemini API connection test successful. Response: API connection successful...
2025-09-05 19:58:53,414 - LicensePlateMonitor - INFO - Testing camera connection...
2025-09-05 19:58:53,414 - LicensePlateMonitor - INFO - Connecting to RTSP camera: rtsp://***:***@***:***@192.168.68.63:1935
2025-09-05 19:58:53,415 - LicensePlateMonitor - ERROR - Error connecting to RTSP camera: module 'cv2' has no attribute 'VideoCapture'
2025-09-05 19:58:53,415 - LicensePlateMonitor - WARNING - Camera connection failed. Checking if we can fall back to mock camera...
2025-09-05 19:58:53,415 - LicensePlateMonitor - INFO - Switching to mock camera for demonstration
2025-09-05 19:58:53,415 - LicensePlateMonitor - INFO - Connecting to mock camera...
2025-09-05 19:58:54,421 - LicensePlateMonitor - INFO - Mock camera connected successfully
2025-09-05 19:58:54,422 - LicensePlateMonitor - INFO - Scheduling image capture and AI processing every 1 minute(s)
2025-09-05 19:58:54,422 - LicensePlateMonitor - INFO - License Plate Monitoring System started successfully!
2025-09-05 19:58:54,422 - LicensePlateMonitor - INFO - System will capture images and detect license plates automatically...
2025-09-05 19:59:08,952 - LicensePlateMonitor - INFO - Received signal 2, shutting down gracefully...
2025-09-05 19:59:08,952 - LicensePlateMonitor - INFO - Stopping License Plate Monitoring System...
2025-09-05 19:59:08,952 - LicensePlateMonitor - INFO - Disconnecting from mock camera
2025-09-05 19:59:08,952 - LicensePlateMonitor - INFO - License Plate Monitoring System stopped.
2025-09-05 19:59:09,469 - LicensePlateMonitor - INFO - Stopping License Plate Monitoring System...
2025-09-05 19:59:09,470 - LicensePlateMonitor - INFO - License Plate Monitoring System stopped.
[2025-09-05 20:03:39] INFO     🎭 Using mock camera for testing
[2025-09-05 20:03:39] INFO     Gemini AI client initialized successfully
[2025-09-05 20:03:39] INFO     Connecting to mock camera...
[2025-09-05 20:03:40] INFO     Mock camera connected successfully
[2025-09-05 20:03:40] INFO     Generated mock image with license plate: MNO678
[2025-09-05 20:03:40] INFO     Mock frame captured and saved: captured_images/mock_capture_20250905_200340.jpg
[2025-09-05 20:03:40] INFO     Processing image for license plate detection: captured_images/mock_capture_20250905_200340.jpg
[2025-09-05 20:03:43] INFO     API call successful: {'success': True, 'license_plates': [{'plate_number': 'MNO678', 'location': 'Center of the image, on what appears to be the rear of a vehicle represented by a blue rectangle.', 'confidence': 'high', 'details': 'The license plate is blue with white text. It appears to be a standard format.'}], 'plates_detected': 1, 'raw_response': 'LICENSE PLATES DETECTED: 1\n\nPlate 1:\n- Number: MNO678\n- Location: Center of the image, on what appears to be the rear of a vehicle represented by a blue rectangle.\n- Confidence: high\n- Details: The license plate is blue with white text. It appears to be a standard format.', 'image_path': 'captured_images/mock_capture_20250905_200340.jpg'}
[2025-09-05 20:03:43] INFO     License plates detected in captured_images/mock_capture_20250905_200340.jpg: [{'plate_number': 'MNO678', 'location': 'Center of the image, on what appears to be the rear of a vehicle represented by a blue rectangle.', 'confidence': 'high', 'details': 'The license plate is blue with white text. It appears to be a standard format.'}]
[2025-09-05 20:03:51] INFO     🎭 Using mock camera for testing
[2025-09-05 20:03:51] INFO     Gemini AI client initialized successfully
[2025-09-05 20:03:51] INFO     🤖 Testing AI processor connection...
[2025-09-05 20:03:52] INFO     Gemini API connection test successful. Response: API connection successful...
[2025-09-05 20:03:52] INFO     📷 Testing camera connection...
[2025-09-05 20:03:52] INFO     Connecting to mock camera...
[2025-09-05 20:03:53] INFO     Mock camera connected successfully
[2025-09-05 20:03:53] INFO     ⏰ Scheduling capture every 1 minute(s)
[2025-09-05 20:04:53] INFO     Generated mock image with license plate: XYZ789
[2025-09-05 20:04:53] INFO     Mock frame captured and saved: captured_images/mock_capture_20250905_200453.jpg
[2025-09-05 20:04:53] INFO     📷 Image captured: mock_capture_20250905_200453.jpg
[2025-09-05 20:04:53] INFO     🤖 Processing with AI...
[2025-09-05 20:04:53] INFO     Processing image for license plate detection: captured_images/mock_capture_20250905_200453.jpg
[2025-09-05 20:04:54] INFO     API call successful: {'success': True, 'license_plates': [{'plate_number': 'XYZ789', 'location': 'Middle of the image, on a blue rectangular object which appears to be a license plate.', 'confidence': 'High', 'details': 'The license plate is on a blue background with white text. The text is clear and legible.'}], 'plates_detected': 1, 'raw_response': 'LICENSE PLATES DETECTED: 1\n\nPlate 1:\n- Number: XYZ789\n- Location: Middle of the image, on a blue rectangular object which appears to be a license plate.\n- Confidence: High\n- Details: The license plate is on a blue background with white text. The text is clear and legible.', 'image_path': 'captured_images/mock_capture_20250905_200453.jpg'}
[2025-09-05 20:04:54] INFO     License plates detected in captured_images/mock_capture_20250905_200453.jpg: [{'plate_number': 'XYZ789', 'location': 'Middle of the image, on a blue rectangular object which appears to be a license plate.', 'confidence': 'High', 'details': 'The license plate is on a blue background with white text. The text is clear and legible.'}]
[2025-09-05 20:04:54] INFO     🎯 Found 1 license plate(s)!
[2025-09-05 20:04:54] INFO     🏷️  Plate 1: XYZ789 | Middle of the image, on a blue rectangular object which appears to be a license plate. | Confidence: High
[2025-09-05 20:04:54] INFO     DETECTED: XYZ789 | Middle of the image, on a blue rectangular object which appears to be a license plate. | High | mock_capture_20250905_200453.jpg
[2025-09-05 20:05:21] INFO     Received signal 15, shutting down gracefully...
[2025-09-05 20:05:21] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 20:05:21] INFO     Disconnecting from mock camera
[2025-09-05 20:05:21] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 20:05:21] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 20:05:21] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 20:05:29] INFO     📹 Initializing real RTSP camera
[2025-09-05 20:05:29] INFO     Gemini AI client initialized successfully
[2025-09-05 20:05:29] INFO     🤖 Testing AI processor connection...
[2025-09-05 20:05:30] INFO     Gemini API connection test successful. Response: API connection successful...
[2025-09-05 20:05:30] INFO     📷 Testing camera connection...
[2025-09-05 20:05:30] INFO     🔄 Attempting to connect to real RTSP camera...
[2025-09-05 20:05:30] INFO     Connecting to RTSP camera: rtsp://***:***@***:***@192.168.68.63:1935
[2025-09-05 20:05:30] ERROR    Error connecting to RTSP camera: module 'cv2' has no attribute 'VideoCapture'
[2025-09-05 20:05:30] WARNING  ❌ Real camera connection failed after all attempts
[2025-09-05 20:05:30] INFO     🎭 Switching to mock camera for demonstration
[2025-09-05 20:05:30] INFO     Connecting to mock camera...
[2025-09-05 20:05:31] INFO     Mock camera connected successfully
[2025-09-05 20:05:31] INFO     ⏰ Scheduling capture every 1 minute(s)
[2025-09-05 20:05:59] INFO     Received signal 15, shutting down gracefully...
[2025-09-05 20:05:59] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 20:05:59] INFO     Disconnecting from mock camera
[2025-09-05 20:05:59] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 20:05:59] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 20:05:59] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 20:08:23] INFO     📹 Initializing real RTSP camera
[2025-09-05 20:08:23] INFO     Gemini AI client initialized successfully
[2025-09-05 20:08:23] INFO     🤖 Testing AI processor connection...
[2025-09-05 20:08:24] INFO     Gemini API connection test successful. Response: API connection successful...
[2025-09-05 20:08:24] INFO     📷 Testing camera connection...
[2025-09-05 20:08:24] INFO     🔄 Attempting to connect to real RTSP camera...
[2025-09-05 20:08:24] INFO     Connecting to RTSP camera: rtsp://***:***@***:***@192.168.68.63:1935
[2025-09-05 20:08:24] ERROR    Error connecting to RTSP camera: module 'cv2' has no attribute 'VideoCapture'
[2025-09-05 20:08:24] WARNING  ❌ Real camera connection failed after all attempts
[2025-09-05 20:08:24] INFO     🎭 Switching to mock camera for demonstration
[2025-09-05 20:08:24] INFO     Connecting to mock camera...
[2025-09-05 20:08:25] INFO     Mock camera connected successfully
[2025-09-05 20:08:25] INFO     ⏰ Scheduling capture every 1 minute(s)
[2025-09-05 20:09:25] INFO     Generated mock image with license plate: XYZ789
[2025-09-05 20:09:25] INFO     Mock frame captured and saved: captured_images/mock_capture_20250905_200925.jpg
[2025-09-05 20:09:25] INFO     📷 Image captured: mock_capture_20250905_200925.jpg
[2025-09-05 20:09:25] INFO     🤖 Processing with AI...
[2025-09-05 20:09:25] INFO     Processing image for license plate detection: captured_images/mock_capture_20250905_200925.jpg
[2025-09-05 20:09:27] INFO     API call successful: {'success': True, 'license_plates': [{'plate_number': 'XYZ789', 'location': 'On a blue license plate, center of the image, on what appears to be a vehicle.', 'confidence': 'High', 'details': 'Blue license plate with white text.'}], 'plates_detected': 1, 'raw_response': 'LICENSE PLATES DETECTED: 1\n\nPlate 1:\n- Number: XYZ789\n- Location: On a blue license plate, center of the image, on what appears to be a vehicle.\n- Confidence: High\n- Details: Blue license plate with white text.', 'image_path': 'captured_images/mock_capture_20250905_200925.jpg'}
[2025-09-05 20:09:27] INFO     License plates detected in captured_images/mock_capture_20250905_200925.jpg: [{'plate_number': 'XYZ789', 'location': 'On a blue license plate, center of the image, on what appears to be a vehicle.', 'confidence': 'High', 'details': 'Blue license plate with white text.'}]
[2025-09-05 20:09:27] INFO     🎯 Found 1 license plate(s)!
[2025-09-05 20:09:27] INFO     🏷️  Plate 1: XYZ789 | On a blue license plate, center of the image, on what appears to be a vehicle. | Confidence: High
[2025-09-05 20:09:27] INFO     DETECTED: XYZ789 | On a blue license plate, center of the image, on what appears to be a vehicle. | High | mock_capture_20250905_200925.jpg
[2025-09-05 20:10:27] INFO     Generated mock image with license plate: VWX567
[2025-09-05 20:10:27] INFO     Mock frame captured and saved: captured_images/mock_capture_20250905_201027.jpg
[2025-09-05 20:10:27] INFO     📷 Image captured: mock_capture_20250905_201027.jpg
[2025-09-05 20:10:27] INFO     🤖 Processing with AI...
[2025-09-05 20:10:27] INFO     Processing image for license plate detection: captured_images/mock_capture_20250905_201027.jpg
[2025-09-05 20:10:30] INFO     API call successful: {'success': True, 'license_plates': [{'plate_number': 'VWX567', 'location': 'On a blue rectangular object in the middle of the road, centered horizontally.', 'confidence': 'high', 'details': 'The license plate is white text on a dark background, within a blue rectangular frame. It appears to be on the front of a vehicle, though the vehicle itself is not visible.'}], 'plates_detected': 1, 'raw_response': 'LICENSE PLATES DETECTED: 1\n\nPlate 1:\n- Number: VWX567\n- Location: On a blue rectangular object in the middle of the road, centered horizontally.\n- Confidence: high\n- Details: The license plate is white text on a dark background, within a blue rectangular frame. It appears to be on the front of a vehicle, though the vehicle itself is not visible.', 'image_path': 'captured_images/mock_capture_20250905_201027.jpg'}
[2025-09-05 20:10:30] INFO     License plates detected in captured_images/mock_capture_20250905_201027.jpg: [{'plate_number': 'VWX567', 'location': 'On a blue rectangular object in the middle of the road, centered horizontally.', 'confidence': 'high', 'details': 'The license plate is white text on a dark background, within a blue rectangular frame. It appears to be on the front of a vehicle, though the vehicle itself is not visible.'}]
[2025-09-05 20:10:30] INFO     🎯 Found 1 license plate(s)!
[2025-09-05 20:10:30] INFO     🏷️  Plate 1: VWX567 | On a blue rectangular object in the middle of the road, centered horizontally. | Confidence: high
[2025-09-05 20:10:30] INFO     DETECTED: VWX567 | On a blue rectangular object in the middle of the road, centered horizontally. | high | mock_capture_20250905_201027.jpg
[2025-09-05 20:11:30] INFO     Generated mock image without license plate
[2025-09-05 20:11:30] INFO     Mock frame captured and saved: captured_images/mock_capture_20250905_201130.jpg
[2025-09-05 20:11:30] INFO     📷 Image captured: mock_capture_20250905_201130.jpg
[2025-09-05 20:11:30] INFO     🤖 Processing with AI...
[2025-09-05 20:11:30] INFO     Processing image for license plate detection: captured_images/mock_capture_20250905_201130.jpg
[2025-09-05 20:11:32] INFO     API call successful: {'success': True, 'license_plates': [], 'plates_detected': 0, 'raw_response': 'LICENSE PLATES DETECTED: 0\nNo license plates visible in this image.', 'image_path': 'captured_images/mock_capture_20250905_201130.jpg'}
[2025-09-05 20:11:32] INFO     No license plates detected in captured_images/mock_capture_20250905_201130.jpg
[2025-09-05 20:11:32] INFO     ℹ️  No license plates detected
[2025-09-05 20:12:32] INFO     Generated mock image with license plate: YZA890
[2025-09-05 20:12:32] INFO     Mock frame captured and saved: captured_images/mock_capture_20250905_201232.jpg
[2025-09-05 20:12:32] INFO     📷 Image captured: mock_capture_20250905_201232.jpg
[2025-09-05 20:12:32] INFO     🤖 Processing with AI...
[2025-09-05 20:12:32] INFO     Processing image for license plate detection: captured_images/mock_capture_20250905_201232.jpg
[2025-09-05 20:12:34] INFO     API call successful: {'success': True, 'license_plates': [{'plate_number': 'YZA890', 'location': 'On the rear of a blue vehicle, on a highway.', 'confidence': 'high', 'details': 'The license plate is white with black text and is rectangular in shape.'}], 'plates_detected': 1, 'raw_response': 'LICENSE PLATES DETECTED: 1\n\nPlate 1:\n- Number: YZA890\n- Location: On the rear of a blue vehicle, on a highway.\n- Confidence: high\n- Details: The license plate is white with black text and is rectangular in shape.', 'image_path': 'captured_images/mock_capture_20250905_201232.jpg'}
[2025-09-05 20:12:34] INFO     License plates detected in captured_images/mock_capture_20250905_201232.jpg: [{'plate_number': 'YZA890', 'location': 'On the rear of a blue vehicle, on a highway.', 'confidence': 'high', 'details': 'The license plate is white with black text and is rectangular in shape.'}]
[2025-09-05 20:12:34] INFO     🎯 Found 1 license plate(s)!
[2025-09-05 20:12:34] INFO     🏷️  Plate 1: YZA890 | On the rear of a blue vehicle, on a highway. | Confidence: high
[2025-09-05 20:12:34] INFO     DETECTED: YZA890 | On the rear of a blue vehicle, on a highway. | high | mock_capture_20250905_201232.jpg
[2025-09-05 20:13:34] INFO     Generated mock image with license plate: DEF456
[2025-09-05 20:13:34] INFO     Mock frame captured and saved: captured_images/mock_capture_20250905_201334.jpg
[2025-09-05 20:13:34] INFO     📷 Image captured: mock_capture_20250905_201334.jpg
[2025-09-05 20:13:34] INFO     🤖 Processing with AI...
[2025-09-05 20:13:34] INFO     Processing image for license plate detection: captured_images/mock_capture_20250905_201334.jpg
[2025-09-05 20:13:36] INFO     API call successful: {'success': True, 'license_plates': [{'plate_number': 'DEF456', 'location': 'Rear of a blue object on the road, possibly a vehicle.', 'confidence': 'high', 'details': 'The license plate is blue with white text.'}], 'plates_detected': 1, 'raw_response': 'LICENSE PLATES DETECTED: 1\n\nPlate 1:\n- Number: DEF456\n- Location: Rear of a blue object on the road, possibly a vehicle.\n- Confidence: high\n- Details: The license plate is blue with white text.', 'image_path': 'captured_images/mock_capture_20250905_201334.jpg'}
[2025-09-05 20:13:36] INFO     License plates detected in captured_images/mock_capture_20250905_201334.jpg: [{'plate_number': 'DEF456', 'location': 'Rear of a blue object on the road, possibly a vehicle.', 'confidence': 'high', 'details': 'The license plate is blue with white text.'}]
[2025-09-05 20:13:36] INFO     🎯 Found 1 license plate(s)!
[2025-09-05 20:13:36] INFO     🏷️  Plate 1: DEF456 | Rear of a blue object on the road, possibly a vehicle. | Confidence: high
[2025-09-05 20:13:36] INFO     DETECTED: DEF456 | Rear of a blue object on the road, possibly a vehicle. | high | mock_capture_20250905_201334.jpg
[2025-09-05 20:14:36] INFO     Generated mock image with license plate: ABC123
[2025-09-05 20:14:36] INFO     Mock frame captured and saved: captured_images/mock_capture_20250905_201436.jpg
[2025-09-05 20:14:36] INFO     📷 Image captured: mock_capture_20250905_201436.jpg
[2025-09-05 20:14:36] INFO     🤖 Processing with AI...
[2025-09-05 20:14:36] INFO     Processing image for license plate detection: captured_images/mock_capture_20250905_201436.jpg
[2025-09-05 20:14:38] INFO     API call successful: {'success': True, 'license_plates': [{'plate_number': 'ABC123', 'location': 'On the rear of a blue object, likely a vehicle, on the road.', 'confidence': 'high', 'details': 'The license plate is blue with white text. The characters appear to be "ABC123".'}], 'plates_detected': 1, 'raw_response': 'LICENSE PLATES DETECTED: 1\n\nPlate 1:\n- Number: ABC123\n- Location: On the rear of a blue object, likely a vehicle, on the road.\n- Confidence: high\n- Details: The license plate is blue with white text. The characters appear to be "ABC123".', 'image_path': 'captured_images/mock_capture_20250905_201436.jpg'}
[2025-09-05 20:14:38] INFO     License plates detected in captured_images/mock_capture_20250905_201436.jpg: [{'plate_number': 'ABC123', 'location': 'On the rear of a blue object, likely a vehicle, on the road.', 'confidence': 'high', 'details': 'The license plate is blue with white text. The characters appear to be "ABC123".'}]
[2025-09-05 20:14:38] INFO     🎯 Found 1 license plate(s)!
[2025-09-05 20:14:38] INFO     🏷️  Plate 1: ABC123 | On the rear of a blue object, likely a vehicle, on the road. | Confidence: high
[2025-09-05 20:14:38] INFO     DETECTED: ABC123 | On the rear of a blue object, likely a vehicle, on the road. | high | mock_capture_20250905_201436.jpg
[2025-09-05 20:15:39] INFO     Generated mock image with license plate: MNO678
[2025-09-05 20:15:39] INFO     Mock frame captured and saved: captured_images/mock_capture_20250905_201539.jpg
[2025-09-05 20:15:39] INFO     📷 Image captured: mock_capture_20250905_201539.jpg
[2025-09-05 20:15:39] INFO     🤖 Processing with AI...
[2025-09-05 20:15:39] INFO     Processing image for license plate detection: captured_images/mock_capture_20250905_201539.jpg
[2025-09-05 20:15:41] INFO     API call successful: {'success': True, 'license_plates': [{'plate_number': 'MNO678', 'location': 'Center of the image, on what appears to be a sign or a stylized representation of a license plate.', 'confidence': 'High', 'details': 'The plate is blue with white text. The surrounding area is grey, representing a road, with white dashed lines. The top of the image shows a timestamp "Mock Camera - 2025-09-05 20:15:39".'}], 'plates_detected': 1, 'raw_response': 'LICENSE PLATES DETECTED: 1\n\nPlate 1:\n- Number: MNO678\n- Location: Center of the image, on what appears to be a sign or a stylized representation of a license plate.\n- Confidence: High\n- Details: The plate is blue with white text. The surrounding area is grey, representing a road, with white dashed lines. The top of the image shows a timestamp "Mock Camera - 2025-09-05 20:15:39".', 'image_path': 'captured_images/mock_capture_20250905_201539.jpg'}
[2025-09-05 20:15:41] INFO     License plates detected in captured_images/mock_capture_20250905_201539.jpg: [{'plate_number': 'MNO678', 'location': 'Center of the image, on what appears to be a sign or a stylized representation of a license plate.', 'confidence': 'High', 'details': 'The plate is blue with white text. The surrounding area is grey, representing a road, with white dashed lines. The top of the image shows a timestamp "Mock Camera - 2025-09-05 20:15:39".'}]
[2025-09-05 20:15:41] INFO     🎯 Found 1 license plate(s)!
[2025-09-05 20:15:41] INFO     🏷️  Plate 1: MNO678 | Center of the image, on what appears to be a sign or a stylized representation of a license plate. | Confidence: High
[2025-09-05 20:15:41] INFO     DETECTED: MNO678 | Center of the image, on what appears to be a sign or a stylized representation of a license plate. | High | mock_capture_20250905_201539.jpg
[2025-09-05 20:15:58] INFO     Received signal 2, shutting down gracefully...
[2025-09-05 20:15:58] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 20:15:58] INFO     Disconnecting from mock camera
[2025-09-05 20:15:58] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 20:15:59] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 20:15:59] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 20:16:26] ERROR    ❌ OpenCV not available - cannot use real RTSP camera
[2025-09-05 20:16:26] WARNING  Failed to initialize real camera: OpenCV not properly installed
[2025-09-05 20:16:26] INFO     🎭 Falling back to mock camera
[2025-09-05 20:16:26] INFO     Gemini AI client initialized successfully
[2025-09-05 20:16:26] INFO     🤖 Testing AI processor connection...
[2025-09-05 20:16:26] INFO     Gemini API connection test successful. Response: API connection successful...
[2025-09-05 20:16:26] INFO     📷 Testing camera connection...
[2025-09-05 20:16:26] INFO     Connecting to mock camera...
[2025-09-05 20:16:27] INFO     Mock camera connected successfully
[2025-09-05 20:16:27] INFO     ⏰ Scheduling capture every 1 minute(s)
[2025-09-05 20:17:28] INFO     Generated mock image with license plate: ABC123
[2025-09-05 20:17:28] INFO     Mock frame captured and saved: captured_images/mock_capture_20250905_201728.jpg
[2025-09-05 20:17:28] INFO     📷 Image captured: mock_capture_20250905_201728.jpg
[2025-09-05 20:17:28] INFO     🤖 Processing with AI...
[2025-09-05 20:17:28] INFO     Processing image for license plate detection: captured_images/mock_capture_20250905_201728.jpg
[2025-09-05 20:17:30] INFO     API call successful: {'success': True, 'license_plates': [{'plate_number': 'ABC123', 'location': 'On the front of a blue rectangular object, positioned on a gray road with white dashed lines. This object is likely a vehicle, though not fully depicted.', 'confidence': 'High', 'details': 'The license plate is white with black text.'}], 'plates_detected': 1, 'raw_response': 'LICENSE PLATES DETECTED: 1\n\nPlate 1:\n- Number: ABC123\n- Location: On the front of a blue rectangular object, positioned on a gray road with white dashed lines. This object is likely a vehicle, though not fully depicted.\n- Confidence: High\n- Details: The license plate is white with black text.', 'image_path': 'captured_images/mock_capture_20250905_201728.jpg'}
[2025-09-05 20:17:30] INFO     License plates detected in captured_images/mock_capture_20250905_201728.jpg: [{'plate_number': 'ABC123', 'location': 'On the front of a blue rectangular object, positioned on a gray road with white dashed lines. This object is likely a vehicle, though not fully depicted.', 'confidence': 'High', 'details': 'The license plate is white with black text.'}]
[2025-09-05 20:17:30] INFO     🎯 Found 1 license plate(s)!
[2025-09-05 20:17:30] INFO     🏷️  Plate 1: ABC123 | On the front of a blue rectangular object, positioned on a gray road with white dashed lines. This object is likely a vehicle, though not fully depicted. | Confidence: High
[2025-09-05 20:17:30] INFO     DETECTED: ABC123 | On the front of a blue rectangular object, positioned on a gray road with white dashed lines. This object is likely a vehicle, though not fully depicted. | High | mock_capture_20250905_201728.jpg
[2025-09-05 20:18:30] INFO     Generated mock image without license plate
[2025-09-05 20:18:30] INFO     Mock frame captured and saved: captured_images/mock_capture_20250905_201830.jpg
[2025-09-05 20:18:30] INFO     📷 Image captured: mock_capture_20250905_201830.jpg
[2025-09-05 20:18:30] INFO     🤖 Processing with AI...
[2025-09-05 20:18:30] INFO     Processing image for license plate detection: captured_images/mock_capture_20250905_201830.jpg
[2025-09-05 20:18:32] INFO     API call successful: {'success': True, 'license_plates': [], 'plates_detected': 0, 'raw_response': 'LICENSE PLATES DETECTED: 0\nNo license plates visible in this image.', 'image_path': 'captured_images/mock_capture_20250905_201830.jpg'}
[2025-09-05 20:18:32] INFO     No license plates detected in captured_images/mock_capture_20250905_201830.jpg
[2025-09-05 20:18:32] INFO     ℹ️  No license plates detected
[2025-09-05 20:19:32] INFO     Generated mock image without license plate
[2025-09-05 20:19:32] INFO     Mock frame captured and saved: captured_images/mock_capture_20250905_201932.jpg
[2025-09-05 20:19:32] INFO     📷 Image captured: mock_capture_20250905_201932.jpg
[2025-09-05 20:19:32] INFO     🤖 Processing with AI...
[2025-09-05 20:19:32] INFO     Processing image for license plate detection: captured_images/mock_capture_20250905_201932.jpg
[2025-09-05 20:19:33] INFO     API call successful: {'success': True, 'license_plates': [], 'plates_detected': 0, 'raw_response': 'LICENSE PLATES DETECTED: 0\nNo license plates visible in this image.', 'image_path': 'captured_images/mock_capture_20250905_201932.jpg'}
[2025-09-05 20:19:33] INFO     No license plates detected in captured_images/mock_capture_20250905_201932.jpg
[2025-09-05 20:19:33] INFO     ℹ️  No license plates detected
[2025-09-05 20:20:34] INFO     Generated mock image with license plate: GHI012
[2025-09-05 20:20:34] INFO     Mock frame captured and saved: captured_images/mock_capture_20250905_202034.jpg
[2025-09-05 20:20:34] INFO     📷 Image captured: mock_capture_20250905_202034.jpg
[2025-09-05 20:20:34] INFO     🤖 Processing with AI...
[2025-09-05 20:20:34] INFO     Processing image for license plate detection: captured_images/mock_capture_20250905_202034.jpg
[2025-09-05 20:20:35] INFO     API call successful: {'success': True, 'license_plates': [{'plate_number': 'GHI012', 'location': "Front of a blue vehicle (indicated by the license plate's background color and shape)", 'confidence': 'high', 'details': 'The license plate has a blue background with white text. The format suggests it could be from a country or region using alphanumeric combinations.'}], 'plates_detected': 1, 'raw_response': "LICENSE PLATES DETECTED: 1\n \nPlate 1:\n- Number: GHI012\n- Location: Front of a blue vehicle (indicated by the license plate's background color and shape)\n- Confidence: high\n- Details: The license plate has a blue background with white text. The format suggests it could be from a country or region using alphanumeric combinations.", 'image_path': 'captured_images/mock_capture_20250905_202034.jpg'}
[2025-09-05 20:20:35] INFO     License plates detected in captured_images/mock_capture_20250905_202034.jpg: [{'plate_number': 'GHI012', 'location': "Front of a blue vehicle (indicated by the license plate's background color and shape)", 'confidence': 'high', 'details': 'The license plate has a blue background with white text. The format suggests it could be from a country or region using alphanumeric combinations.'}]
[2025-09-05 20:20:35] INFO     🎯 Found 1 license plate(s)!
[2025-09-05 20:20:35] INFO     🏷️  Plate 1: GHI012 | Front of a blue vehicle (indicated by the license plate's background color and shape) | Confidence: high
[2025-09-05 20:20:35] INFO     DETECTED: GHI012 | Front of a blue vehicle (indicated by the license plate's background color and shape) | high | mock_capture_20250905_202034.jpg
[2025-09-05 20:21:36] INFO     Generated mock image without license plate
[2025-09-05 20:21:36] INFO     Mock frame captured and saved: captured_images/mock_capture_20250905_202136.jpg
[2025-09-05 20:21:36] INFO     📷 Image captured: mock_capture_20250905_202136.jpg
[2025-09-05 20:21:36] INFO     🤖 Processing with AI...
[2025-09-05 20:21:36] INFO     Processing image for license plate detection: captured_images/mock_capture_20250905_202136.jpg
[2025-09-05 20:21:38] INFO     API call successful: {'success': True, 'license_plates': [], 'plates_detected': 0, 'raw_response': 'LICENSE PLATES DETECTED: 0\nNo license plates visible in this image.', 'image_path': 'captured_images/mock_capture_20250905_202136.jpg'}
[2025-09-05 20:21:38] INFO     No license plates detected in captured_images/mock_capture_20250905_202136.jpg
[2025-09-05 20:21:38] INFO     ℹ️  No license plates detected

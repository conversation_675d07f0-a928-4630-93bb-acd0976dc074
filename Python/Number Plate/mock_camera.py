"""
Mock Camera Handler for testing without OpenCV/RTSP camera
"""
import os
import time
import json
from datetime import datetime
from PIL import Image, ImageDraw, ImageFont
import random

from logger import get_logger


class MockCameraHandler:
    """Mock camera that generates test images with license plates"""
    
    def __init__(self, config_path="config.json"):
        self.config = self._load_config(config_path)
        self.logger = get_logger()
        self.connected = False
        
        # Sample license plates for testing
        self.sample_plates = [
            "ABC123", "XYZ789", "DEF456", "GHI012", "JKL345",
            "MNO678", "PQR901", "STU234", "VWX567", "YZA890"
        ]
        
        # Ensure images directory exists
        self.images_dir = self.config.get('storage', {}).get('images_directory', 'captured_images')
        os.makedirs(self.images_dir, exist_ok=True)
    
    def _load_config(self, config_path):
        """Load configuration from JSON file"""
        try:
            with open(config_path, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            self.logger.error(f"Config file {config_path} not found")
            return {}
    
    def connect(self) -> bool:
        """Mock connection to camera"""
        try:
            self.logger.info("Connecting to mock camera...")
            time.sleep(1)  # Simulate connection time
            self.connected = True
            self.logger.info("Mock camera connected successfully")
            return True
        except Exception as e:
            self.logger.error(f"Failed to connect to mock camera: {str(e)}")
            return False
    
    def disconnect(self):
        """Mock disconnection from camera"""
        if self.connected:
            self.logger.info("Disconnecting from mock camera")
            self.connected = False
    
    def is_connected(self) -> bool:
        """Check if mock camera is connected"""
        return self.connected
    
    def capture_frame(self) -> tuple:
        """Capture a mock frame with license plate"""
        if not self.connected:
            self.logger.error("Mock camera not connected")
            return False, None
        
        try:
            # Create a mock image with a license plate
            image = self._generate_mock_image()
            
            # Save the image
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"mock_capture_{timestamp}.jpg"
            filepath = os.path.join(self.images_dir, filename)
            
            image.save(filepath, "JPEG", quality=95)
            self.logger.info(f"Mock frame captured and saved: {filepath}")
            
            return True, filepath
            
        except Exception as e:
            self.logger.error(f"Failed to capture mock frame: {str(e)}")
            return False, None
    
    def _generate_mock_image(self) -> Image.Image:
        """Generate a mock image with a license plate"""
        # Create a 800x600 image with a road scene background
        width, height = 800, 600
        image = Image.new('RGB', (width, height), color='lightblue')
        draw = ImageDraw.Draw(image)
        
        # Draw a simple road
        road_color = 'gray'
        draw.rectangle([0, height//2, width, height], fill=road_color)
        
        # Draw lane markings
        for x in range(0, width, 100):
            draw.rectangle([x, height//2 + 50, x + 50, height//2 + 60], fill='white')
        
        # Randomly decide if there should be a car (and license plate)
        has_car = random.choice([True, True, False])  # 66% chance of having a car
        
        if has_car:
            # Draw a simple car
            car_x = random.randint(100, width - 200)
            car_y = height//2 + 20
            car_width = 150
            car_height = 80
            
            # Car body
            draw.rectangle([car_x, car_y, car_x + car_width, car_y + car_height], 
                         fill='blue', outline='darkblue', width=2)
            
            # Car windows
            draw.rectangle([car_x + 10, car_y + 10, car_x + car_width - 10, car_y + 30], 
                         fill='lightblue', outline='darkblue')
            
            # License plate
            plate_width = 80
            plate_height = 20
            plate_x = car_x + (car_width - plate_width) // 2
            plate_y = car_y + car_height - 25
            
            # License plate background
            draw.rectangle([plate_x, plate_y, plate_x + plate_width, plate_y + plate_height], 
                         fill='white', outline='black', width=2)
            
            # License plate text
            plate_text = random.choice(self.sample_plates)
            
            try:
                # Try to use a font, fall back to default if not available
                font = ImageFont.truetype("Arial.ttf", 12)
            except:
                font = ImageFont.load_default()
            
            # Calculate text position to center it
            bbox = draw.textbbox((0, 0), plate_text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            text_x = plate_x + (plate_width - text_width) // 2
            text_y = plate_y + (plate_height - text_height) // 2
            
            draw.text((text_x, text_y), plate_text, fill='black', font=font)
            
            self.logger.info(f"Generated mock image with license plate: {plate_text}")
        else:
            self.logger.info("Generated mock image without license plate")
        
        # Add timestamp
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        try:
            font = ImageFont.truetype("Arial.ttf", 16)
        except:
            font = ImageFont.load_default()
        
        draw.text((10, 10), f"Mock Camera - {timestamp}", fill='black', font=font)
        
        return image
    
    def __enter__(self):
        """Context manager entry"""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.disconnect()


if __name__ == "__main__":
    # Test the mock camera
    print("Testing Mock Camera...")
    
    with MockCameraHandler() as camera:
        if camera.is_connected():
            print("✓ Mock camera connected")
            
            # Capture a few test frames
            for i in range(3):
                success, filepath = camera.capture_frame()
                if success:
                    print(f"✓ Captured frame {i+1}: {filepath}")
                else:
                    print(f"✗ Failed to capture frame {i+1}")
                time.sleep(1)
        else:
            print("✗ Mock camera connection failed")
    
    print("Mock camera test completed")

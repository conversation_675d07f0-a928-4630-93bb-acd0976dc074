"""
RTSP Camera Handler for License Plate Monitoring System
"""
import os
import json
import time
from datetime import datetime
from typing import Optional, <PERSON><PERSON>
from logger import get_logger, get_license_plate_logger

# Import OpenCV with error handling
try:
    import cv2
    OPENCV_AVAILABLE = True
    # Test if VideoCapture is available
    if not hasattr(cv2, 'VideoCapture'):
        OPENCV_AVAILABLE = False
        print("⚠️  OpenCV imported but VideoCapture not available")
except ImportError:
    OPENCV_AVAILABLE = False
    print("❌ OpenCV not available - install with: pip install opencv-python==********")


class RTSPCameraHandler:
    def __init__(self, config_path="config.json"):
        """Initialize RTSP camera handler"""
        self.config = self._load_config(config_path)
        self.logger = get_logger()
        self.license_plate_logger = get_license_plate_logger()
        self.cap = None
        self.is_connected = False

        # Check OpenCV availability
        if not OPENCV_AVAILABLE:
            self.logger.error("❌ OpenCV not available - cannot use real RTSP camera")
            raise ImportError("OpenCV not properly installed")
        
        # Create images directory
        images_dir = self.config.get('storage', {}).get('images_directory', 'captured_images')
        os.makedirs(images_dir, exist_ok=True)
    
    def _load_config(self, config_path):
        """Load configuration from JSON file"""
        try:
            with open(config_path, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            self.logger.error(f"Config file {config_path} not found")
            return self._get_default_config()
    
    def _get_default_config(self):
        """Get default configuration"""
        return {
            "rtsp": {
                "url": "rtsp://localhost:554/stream",
                "username": "",
                "password": "",
                "timeout": 10
            },
            "capture": {
                "interval_minutes": 1,
                "save_images": True,
                "image_quality": 95
            },
            "storage": {
                "images_directory": "captured_images",
                "keep_images_days": 7
            }
        }
    
    def connect(self, max_retries=3, retry_delay=2) -> bool:
        """Connect to RTSP camera with robust retry logic"""
        if not OPENCV_AVAILABLE:
            self.logger.error("❌ Cannot connect: OpenCV not properly installed")
            self.logger.info("💡 Fix with: pip uninstall opencv-python opencv-contrib-python -y && pip install opencv-python==********")
            return False

        rtsp_config = self.config.get('rtsp', {})
        rtsp_url = rtsp_config.get('url', '')

        if not rtsp_url:
            self.logger.error("RTSP URL not configured")
            return False

        # Add authentication if provided
        username = rtsp_config.get('username', '')
        password = rtsp_config.get('password', '')

        if username and password:
            # Insert credentials into URL
            if '://' in rtsp_url:
                protocol, rest = rtsp_url.split('://', 1)
                rtsp_url = f"{protocol}://{username}:{password}@{rest}"

        # Retry connection with exponential backoff
        for retry in range(max_retries):
            try:
                if retry > 0:
                    delay = retry_delay * (2 ** (retry - 1))  # Exponential backoff
                    self.logger.info(f"🔄 Retry {retry + 1}/{max_retries} in {delay} seconds...")
                    time.sleep(delay)

                self.logger.info(f"🔌 Connecting to RTSP camera: {rtsp_url.replace(password, '***') if password else rtsp_url}")

                # Ensure any previous connection is closed
                if self.cap:
                    self.cap.release()
                    self.cap = None

                # Create VideoCapture object with backend preference
                self.cap = cv2.VideoCapture(rtsp_url, cv2.CAP_FFMPEG)

                # Set timeout and buffer properties for better RTSP handling
                timeout = rtsp_config.get('timeout', 15) * 1000  # Convert to milliseconds
                self.cap.set(cv2.CAP_PROP_OPEN_TIMEOUT_MSEC, timeout)
                self.cap.set(cv2.CAP_PROP_READ_TIMEOUT_MSEC, timeout)

                # Reduce buffer size to minimize latency
                self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)

                # Set additional properties for RTSP streams
                self.cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc('M', 'J', 'P', 'G'))

                # Test connection by reading frames
                if self.cap.isOpened():
                    # Try reading multiple frames to ensure stable connection
                    successful_reads = 0
                    for attempt in range(5):  # Try up to 5 frame reads
                        ret, frame = self.cap.read()
                        if ret and frame is not None and frame.size > 0:
                            successful_reads += 1
                            if successful_reads >= 2:  # Need at least 2 successful reads
                                self.is_connected = True
                                self.logger.info(f"✅ Successfully connected to RTSP camera (retry {retry + 1}, {successful_reads} successful reads)")
                                self.logger.info(f"📐 Frame size: {frame.shape}")
                                return True
                        time.sleep(0.5)  # Short delay between frame reads

                    self.logger.warning(f"⚠️ Connected to RTSP but only {successful_reads}/5 frame reads successful")
                    if successful_reads > 0:
                        # Accept connection if we got at least one frame
                        self.is_connected = True
                        self.logger.info(f"✅ Accepting connection with {successful_reads} successful reads")
                        return True
                else:
                    self.logger.warning(f"⚠️ Failed to open RTSP stream (attempt {retry + 1})")

            except Exception as e:
                self.logger.warning(f"⚠️ Connection attempt {retry + 1} failed: {str(e)}")

            # Clean up failed connection
            if self.cap:
                self.cap.release()
                self.cap = None
            self.is_connected = False

        self.logger.error(f"❌ Failed to connect to RTSP camera after {max_retries} attempts")
        return False
    
    def disconnect(self):
        """Disconnect from RTSP camera"""
        if self.cap:
            self.cap.release()
            self.cap = None
        self.is_connected = False
        self.logger.info("🔌 Disconnected from RTSP camera")

    def check_connection_health(self) -> bool:
        """Check if the camera connection is still healthy"""
        if not self.is_connected or not self.cap:
            return False

        try:
            # Check if the capture object is still opened
            if not self.cap.isOpened():
                self.logger.warning("⚠️ Camera connection lost - capture object not opened")
                self.is_connected = False
                return False

            # Try to read a frame to test the connection
            ret, frame = self.cap.read()
            if not ret or frame is None or frame.size == 0:
                self.logger.warning("⚠️ Camera connection unhealthy - cannot read frame")
                return False

            return True

        except Exception as e:
            self.logger.warning(f"⚠️ Connection health check failed: {str(e)}")
            self.is_connected = False
            return False
    
    def capture_image(self, auto_reconnect=True) -> Optional[Tuple[str, any]]:
        """Capture image from RTSP camera with automatic reconnection"""
        if not self.is_connected or not self.cap:
            if auto_reconnect:
                self.logger.info("🔄 Camera not connected, attempting to reconnect...")
                if not self.connect():
                    self.logger.error("❌ Failed to reconnect to camera")
                    return None
            else:
                self.logger.error("❌ Camera not connected")
                return None

        # Check connection health before capture
        if not self.check_connection_health():
            if auto_reconnect:
                self.logger.info("🔄 Connection unhealthy, attempting to reconnect...")
                self.disconnect()  # Clean disconnect first
                if not self.connect():
                    self.logger.error("❌ Failed to reconnect after health check failure")
                    return None
            else:
                self.logger.error("❌ Connection unhealthy")
                return None

        try:
            # Try to read frame with multiple attempts
            for attempt in range(3):
                ret, frame = self.cap.read()

                if ret and frame is not None and frame.size > 0:
                    # Generate filename with timestamp
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    images_dir = self.config.get('storage', {}).get('images_directory', 'captured_images')
                    filename = f"capture_{timestamp}.jpg"
                    filepath = os.path.join(images_dir, filename)

                    # Save image
                    quality = self.config.get('capture', {}).get('image_quality', 95)
                    success = cv2.imwrite(filepath, frame, [cv2.IMWRITE_JPEG_QUALITY, quality])

                    if success:
                        self.logger.info(f"✅ Image captured and saved: {filepath}")
                        self.license_plate_logger.log_capture_event(True, image_path=filepath)
                        return filepath, frame
                    else:
                        self.logger.error(f"❌ Failed to save image: {filepath}")
                        self.license_plate_logger.log_capture_event(False, error="Failed to save image")
                        return None
                else:
                    self.logger.warning(f"⚠️ Failed to capture frame (attempt {attempt + 1}/3)")
                    if attempt < 2:  # Don't sleep on the last attempt
                        time.sleep(0.5)

            # All attempts failed
            self.logger.error("❌ Failed to capture frame after 3 attempts")
            self.license_plate_logger.log_capture_event(False, error="Failed to read frame after retries")
            return None
                
        except Exception as e:
            self.logger.error(f"Error capturing image: {str(e)}")
            self.license_plate_logger.log_capture_event(False, error=str(e))
            return None
    
    def test_connection(self) -> bool:
        """Test camera connection and capture a test image"""
        self.logger.info("Testing RTSP camera connection...")
        
        if self.connect():
            result = self.capture_image()
            self.disconnect()
            
            if result:
                self.logger.info("Camera test successful!")
                return True
            else:
                self.logger.error("Camera test failed - could not capture image")
                return False
        else:
            self.logger.error("Camera test failed - could not connect")
            return False
    
    def __enter__(self):
        """Context manager entry"""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.disconnect()


if __name__ == "__main__":
    # Test the camera handler
    camera = RTSPCameraHandler()
    camera.test_connection()

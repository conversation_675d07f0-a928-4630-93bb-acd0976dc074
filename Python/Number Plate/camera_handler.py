"""
RTSP Camera Handler for License Plate Monitoring System
"""
import os
import json
import time
from datetime import datetime
from typing import Optional, <PERSON><PERSON>
from logger import get_logger, get_license_plate_logger

# Import OpenCV with error handling
try:
    import cv2
    OPENCV_AVAILABLE = True
    # Test if VideoCapture is available
    if not hasattr(cv2, 'VideoCapture'):
        OPENCV_AVAILABLE = False
        print("⚠️  OpenCV imported but VideoCapture not available")
except ImportError:
    OPENCV_AVAILABLE = False
    print("❌ OpenCV not available - install with: pip install opencv-python==********")


class RTSPCameraHandler:
    def __init__(self, config_path="config.json"):
        """Initialize RTSP camera handler"""
        self.config = self._load_config(config_path)
        self.logger = get_logger()
        self.license_plate_logger = get_license_plate_logger()
        self.cap = None
        self.is_connected = False

        # Check OpenCV availability
        if not OPENCV_AVAILABLE:
            self.logger.error("❌ OpenCV not available - cannot use real RTSP camera")
            raise ImportError("OpenCV not properly installed")
        
        # Create images directory
        images_dir = self.config.get('storage', {}).get('images_directory', 'captured_images')
        os.makedirs(images_dir, exist_ok=True)
    
    def _load_config(self, config_path):
        """Load configuration from JSON file"""
        try:
            with open(config_path, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            self.logger.error(f"Config file {config_path} not found")
            return self._get_default_config()
    
    def _get_default_config(self):
        """Get default configuration"""
        return {
            "rtsp": {
                "url": "rtsp://localhost:554/stream",
                "username": "",
                "password": "",
                "timeout": 10
            },
            "capture": {
                "interval_minutes": 1,
                "save_images": True,
                "image_quality": 95
            },
            "storage": {
                "images_directory": "captured_images",
                "keep_images_days": 7
            }
        }
    
    def connect(self) -> bool:
        """Connect to RTSP camera"""
        if not OPENCV_AVAILABLE:
            self.logger.error("❌ Cannot connect: OpenCV not properly installed")
            self.logger.info("💡 Fix with: pip uninstall opencv-python opencv-contrib-python -y && pip install opencv-python==********")
            return False

        try:
            rtsp_config = self.config.get('rtsp', {})
            rtsp_url = rtsp_config.get('url', '')

            if not rtsp_url:
                self.logger.error("RTSP URL not configured")
                return False

            # Add authentication if provided
            username = rtsp_config.get('username', '')
            password = rtsp_config.get('password', '')

            if username and password:
                # Insert credentials into URL
                if '://' in rtsp_url:
                    protocol, rest = rtsp_url.split('://', 1)
                    rtsp_url = f"{protocol}://{username}:{password}@{rest}"

            self.logger.info(f"Connecting to RTSP camera: {rtsp_url.replace(password, '***') if password else rtsp_url}")

            # Create VideoCapture object with backend preference
            self.cap = cv2.VideoCapture(rtsp_url, cv2.CAP_FFMPEG)

            # Set timeout and buffer properties for better RTSP handling
            timeout = rtsp_config.get('timeout', 10) * 1000  # Convert to milliseconds
            self.cap.set(cv2.CAP_PROP_OPEN_TIMEOUT_MSEC, timeout)
            self.cap.set(cv2.CAP_PROP_READ_TIMEOUT_MSEC, timeout)

            # Reduce buffer size to minimize latency
            self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)

            # Set additional properties for RTSP streams
            self.cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc('M', 'J', 'P', 'G'))

            # Test connection by reading a frame
            if self.cap.isOpened():
                # Try reading a frame with timeout
                for attempt in range(3):  # Try up to 3 times
                    ret, frame = self.cap.read()
                    if ret and frame is not None and frame.size > 0:
                        self.is_connected = True
                        self.logger.info(f"Successfully connected to RTSP camera (attempt {attempt + 1})")
                        self.logger.info(f"Frame size: {frame.shape}")
                        return True
                    time.sleep(1)  # Wait between attempts

                self.logger.error("Connected to RTSP but failed to read valid frame after 3 attempts")
                self.disconnect()
                return False
            else:
                self.logger.error("Failed to open RTSP stream")
                return False

        except Exception as e:
            self.logger.error(f"Error connecting to RTSP camera: {str(e)}")
            return False
    
    def disconnect(self):
        """Disconnect from RTSP camera"""
        if self.cap:
            self.cap.release()
            self.cap = None
        self.is_connected = False
        self.logger.info("Disconnected from RTSP camera")
    
    def capture_image(self) -> Optional[Tuple[str, any]]:
        """Capture image from RTSP camera"""
        if not self.is_connected or not self.cap:
            self.logger.error("Camera not connected")
            return None
        
        try:
            # Read frame from camera
            ret, frame = self.cap.read()
            
            if not ret or frame is None:
                self.logger.error("Failed to capture frame from camera")
                self.license_plate_logger.log_capture_event(False, error="Failed to read frame")
                return None
            
            # Generate filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            images_dir = self.config.get('storage', {}).get('images_directory', 'captured_images')
            filename = f"capture_{timestamp}.jpg"
            filepath = os.path.join(images_dir, filename)
            
            # Save image
            quality = self.config.get('capture', {}).get('image_quality', 95)
            success = cv2.imwrite(filepath, frame, [cv2.IMWRITE_JPEG_QUALITY, quality])
            
            if success:
                self.logger.info(f"Image captured and saved: {filepath}")
                self.license_plate_logger.log_capture_event(True, image_path=filepath)
                return filepath, frame
            else:
                self.logger.error(f"Failed to save image: {filepath}")
                self.license_plate_logger.log_capture_event(False, error="Failed to save image")
                return None
                
        except Exception as e:
            self.logger.error(f"Error capturing image: {str(e)}")
            self.license_plate_logger.log_capture_event(False, error=str(e))
            return None
    
    def test_connection(self) -> bool:
        """Test camera connection and capture a test image"""
        self.logger.info("Testing RTSP camera connection...")
        
        if self.connect():
            result = self.capture_image()
            self.disconnect()
            
            if result:
                self.logger.info("Camera test successful!")
                return True
            else:
                self.logger.error("Camera test failed - could not capture image")
                return False
        else:
            self.logger.error("Camera test failed - could not connect")
            return False
    
    def __enter__(self):
        """Context manager entry"""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.disconnect()


if __name__ == "__main__":
    # Test the camera handler
    camera = RTSPCameraHandler()
    camera.test_connection()
